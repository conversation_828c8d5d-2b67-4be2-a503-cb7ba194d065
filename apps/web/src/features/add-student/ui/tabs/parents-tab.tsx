'use client';

import type { AddStudentSchema, ParentSchema } from '@lilypad/api/schemas/students';
import {
  type ParentRelationshipEnum,
  ParentRelationshipEnumMap,
} from '@lilypad/db/enums';
import { normalizePhone } from '@lilypad/shared/phone';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import { If } from '@lilypad/ui/components/if';
import { Label } from '@lilypad/ui/components/label';
import {
  EditIcon,
  MailIcon,
  PhoneIcon,
  PlusIcon,
  ShieldUserIcon,
  Trash2Icon,
  UserIcon,
} from 'lucide-react';
import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { ParentFormFields } from '@/shared/ui/students/parents/parent-form-fields';

const EMPTY_PARENT: Partial<ParentSchema> = {
  firstName: '',
  middleName: '',
  lastName: '',
  relationshipType: undefined,
  primaryEmail: '',
  primaryPhone: '',
  secondaryEmail: '',
  secondaryPhone: '',
  isPrimaryContact: false,
  hasPickupAuthorization: false,
};

export function ParentsTab() {
  const form = useFormContext<AddStudentSchema>();
  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: 'parents',
  });

  const [showAddForm, setShowAddForm] = React.useState(false);
  const [editingIndex, setEditingIndex] = React.useState<number | null>(null);
  const [parentData, setParentData] =
    React.useState<Partial<ParentSchema>>(EMPTY_PARENT);

  const isEditing = editingIndex !== null;
  const isShowingForm = showAddForm || isEditing;

  const handleParentFieldChange = (
    field: keyof ParentSchema,
    value: string | boolean
  ) => {
    setParentData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddParent = () => {
    if (
      !(
        parentData.firstName &&
        parentData.lastName &&
        parentData.relationshipType
      )
    ) {
      return;
    }

    const newParent: ParentSchema = {
      firstName: parentData.firstName,
      middleName: parentData.middleName || undefined,
      lastName: parentData.lastName,
      relationshipType: parentData.relationshipType,
      primaryEmail: parentData.primaryEmail || undefined,
      primaryPhone: parentData.primaryPhone || undefined,
      secondaryEmail: parentData.secondaryEmail || undefined,
      secondaryPhone: parentData.secondaryPhone || undefined,
      isPrimaryContact:
        fields.length === 0 ? true : Boolean(parentData.isPrimaryContact),
      hasPickupAuthorization: Boolean(parentData.hasPickupAuthorization),
    };

    append(newParent);
    resetForm();
  };

  const handleUpdateParent = () => {
    if (
      editingIndex === null ||
      !(
        parentData.firstName &&
        parentData.lastName &&
        parentData.relationshipType
      )
    ) {
      return;
    }

    const updatedParent: ParentSchema = {
      firstName: parentData.firstName,
      middleName: parentData.middleName || undefined,
      lastName: parentData.lastName,
      relationshipType: parentData.relationshipType,
      primaryEmail: parentData.primaryEmail || undefined,
      primaryPhone: parentData.primaryPhone || undefined,
      secondaryEmail: parentData.secondaryEmail || undefined,
      secondaryPhone: parentData.secondaryPhone || undefined,
      isPrimaryContact: Boolean(parentData.isPrimaryContact),
      hasPickupAuthorization: Boolean(parentData.hasPickupAuthorization),
    };

    update(editingIndex, updatedParent);
    resetForm();
  };

  const handleEditParent = (index: number) => {
    const parent = fields[index];
    setParentData({
      ...parent,
      primaryPhone: normalizePhone(parent.primaryPhone),
      secondaryPhone: normalizePhone(parent.secondaryPhone),
    });
    setEditingIndex(index);
    setShowAddForm(false);
  };

  const resetForm = () => {
    setParentData(EMPTY_PARENT);
    setShowAddForm(false);
    setEditingIndex(null);
  };

  const handleCancel = () => {
    resetForm();
  };

  const handleSubmit = () => {
    if (isEditing) {
      handleUpdateParent();
    } else {
      handleAddParent();
    }
  };

  return (
    <div className="space-y-6">
      <If condition={isShowingForm}>
        <Card className="gap-2 border-none p-0 shadow-none">
          <CardHeader className="p-0">
            <CardTitle className="text-base">
              {isEditing ? 'Edit Parent/Guardian' : 'Add Parent/Guardian'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-0">
            <ParentFormFields
              data={parentData}
              firstParent={fields.length === 0 && !isEditing}
              onChange={handleParentFieldChange}
            />
            <div className="flex justify-end gap-2">
              <Button
                onClick={handleCancel}
                size="sm"
                type="button"
                variant="outline"
              >
                Cancel
              </Button>
              <Button
                disabled={
                  !(
                    parentData.firstName &&
                    parentData.lastName &&
                    parentData.relationshipType
                  )
                }
                onClick={handleSubmit}
                size="sm"
                type="button"
              >
                {isEditing ? 'Update Parent/Guardian' : 'Add Parent/Guardian'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </If>

      <If condition={!isShowingForm && fields.length > 0}>
        <div className="grid grid-cols-1 gap-4">
          {fields.map((field, index) => (
            <Card
              className="relative gap-0 px-0 py-4 shadow-none"
              key={field.id}
            >
              <CardHeader className="px-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <UserIcon className="size-4 text-muted-foreground" />
                    <CardTitle className="text-base">
                      {field.firstName} {field.middleName} {field.lastName}
                    </CardTitle>
                    <Badge variant="outline">
                      {
                        ParentRelationshipEnumMap[
                        field.relationshipType as ParentRelationshipEnum
                        ]
                      }
                    </Badge>
                    <If condition={field.isPrimaryContact}>
                      <Badge
                        className="gap-1 border-primary bg-primary/10 text-primary"
                        variant="outline"
                      >
                        Primary Contact
                      </Badge>
                    </If>
                    <If condition={field.hasPickupAuthorization}>
                      <Badge className="text-xs" variant="secondary">
                        Pickup Authorized
                      </Badge>
                    </If>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      className="size-8 p-0"
                      onClick={() => handleEditParent(index)}
                      size="sm"
                      type="button"
                      variant="ghost"
                    >
                      <EditIcon className="size-4" />
                    </Button>
                    <Button
                      className="size-8 p-0"
                      disabled={fields.length === 1}
                      onClick={() => remove(index)}
                      size="sm"
                      type="button"
                      variant="cancel"
                    >
                      <Trash2Icon className="size-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2 px-4 text-sm">
                <If condition={field.primaryEmail}>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <MailIcon className="size-3" />
                    <Label>Primary Email</Label>
                    <span className="text-foreground">
                      {field.primaryEmail}
                    </span>
                  </div>
                </If>
                <If condition={field.primaryPhone}>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <PhoneIcon className="size-3" />
                    <Label>Primary Phone</Label>
                    <span className="text-foreground">
                      {field.primaryPhone}
                    </span>
                  </div>
                </If>
                <If condition={field.secondaryEmail}>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <MailIcon className="size-3" />
                    <Label>Secondary Email</Label>
                    <span className="text-foreground">
                      {field.secondaryEmail}
                    </span>
                  </div>
                </If>
                <If condition={field.secondaryPhone}>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <PhoneIcon className="size-3" />
                    <Label>Secondary Phone</Label>
                    <span className="text-foreground">
                      {field.secondaryPhone}
                    </span>
                  </div>
                </If>
              </CardContent>
            </Card>
          ))}
          <Card
            className="cursor-pointer border-dashed text-muted-foreground shadow-none hover:bg-muted-foreground/5 hover:text-primary"
            onClick={() => setShowAddForm(true)}
          >
            <CardContent className="flex h-full items-center justify-center gap-2 p-6 text-sm transition">
              <PlusIcon className="size-4" />
              <span>Add Parent/Guardian</span>
            </CardContent>
          </Card>
        </div>
      </If>

      <If condition={fields.length === 0 && !isShowingForm}>
        <div className="rounded-lg border border-dashed p-8 text-center">
          <div className="mx-auto flex w-fit items-center justify-center rounded-lg bg-muted p-2.5">
            <ShieldUserIcon className="size-5 text-muted-foreground" />
          </div>
          <h3 className="mt-4 font-medium text-lg">
            No parents or guardians added
          </h3>
          <p className="mt-2 text-muted-foreground text-sm">
            Add at least one parent or guardian for the student. One must be
            marked as the primary contact.
          </p>
          <Button
            className="mt-4"
            onClick={() => setShowAddForm(true)}
            size="sm"
            type="button"
            variant="outline"
          >
            Add Parent/Guardian
          </Button>
        </div>
      </If>
    </div>
  );
}
