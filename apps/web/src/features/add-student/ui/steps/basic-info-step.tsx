'use client';

import type { AddStudentSchema } from '@lilypad/api/schemas/students';
import { GenderEnum, SchoolGradeEnum } from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import { Calendar } from '@lilypad/ui/components/calendar';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import { OptionalBadge } from '@lilypad/ui/components/optional-badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Toggle } from '@lilypad/ui/components/toggle';
import { cn } from '@lilypad/ui/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, SquareCheckIcon, SquareDashedIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import { LanguageMultiSelectField, SchoolSelect } from '@/shared/ui/students';
import { SchoolGradeBadge } from '@/shared/ui/students/school-grade-badge';
import { StudentGenderBadge } from '@/shared/ui/students/student-gender-badge';

export function BasicInfoStep() {
  const form = useFormContext<AddStudentSchema>();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="font-semibold text-lg">Basic Information</h2>
        <p className="text-muted-foreground text-sm">
          Enter the student's basic information.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>First Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter first name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Last Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter last name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="middleName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Middle Name <OptionalBadge />
              </FormLabel>
              <FormControl>
                <Input placeholder="Enter middle name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="preferredName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Preferred Name <OptionalBadge />
              </FormLabel>
              <FormControl>
                <Input placeholder="Enter preferred name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="studentIdNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Student ID</FormLabel>
              <FormControl>
                <Input placeholder="Enter student ID" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="gender"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Gender</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(GenderEnum).map((gender) => (
                    <SelectItem key={gender} value={gender}>
                      <StudentGenderBadge gender={gender} />
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="dateOfBirth"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Date of Birth</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      className={cn(
                        'w-full pl-3 text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                      variant="outline"
                    >
                      {field.value ? (
                        format(field.value, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent align="start" className="w-auto p-0">
                  <Calendar
                    captionLayout="dropdown"
                    disabled={(date) =>
                      date > new Date() || date < new Date('1900-01-01')
                    }
                    mode="single"
                    onSelect={field.onChange}
                    selected={field.value}
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <SchoolSelect
          fieldName="primarySchoolId"
          label="Primary School"
          placeholder="Select primary school"
          required
        />

        <FormField
          control={form.control}
          name="outOfDistrict"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Out of District</FormLabel>
              <FormControl>
                <Toggle
                  className="w-full justify-start px-2 data-[state=on]:border-primary"
                  onPressedChange={(pressed: boolean) => {
                    field.onChange(pressed);
                    if (!pressed) {
                      form.setValue('privateSchool', '');
                    }
                  }}
                  pressed={field.value}
                  variant="outline"
                >
                  {field.value ? (
                    <SquareCheckIcon className="size-4 text-primary" />
                  ) : (
                    <SquareDashedIcon className="size-4 text-muted-foreground" />
                  )}
                  <span className="text-sm">
                    Student is out of district
                  </span>
                </Toggle>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="privateSchool"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Private School
              </FormLabel>
              <FormControl>
                <Input
                  disabled={!form.watch('outOfDistrict')}
                  placeholder="Enter private school name"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="grade"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Grade <OptionalBadge />
              </FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select grade" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(SchoolGradeEnum).map((grade) => (
                    <SelectItem key={grade} value={grade}>
                      <SchoolGradeBadge grade={grade} />
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <LanguageMultiSelectField
          label="Languages"
          languageIdsFieldName="languageIds"
          primaryLanguageIdFieldName="primaryLanguageId"
          required
        />
      </div>
    </div>
  );
}