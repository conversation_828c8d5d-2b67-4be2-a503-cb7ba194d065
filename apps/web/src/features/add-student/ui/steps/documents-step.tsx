'use client';

import type { AddStudentSchema, DocumentSchema } from '@lilypad/api/schemas/students';
import {
  DocumentCategoryEnum,
  DocumentCategoryEnumMap,
} from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { If } from '@lilypad/ui/components/if';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { FilePdfIcon } from '@lilypad/ui/icons';
import {
  FileIcon,
  FileSpreadsheetIcon,
  FileTextIcon,
  InfoIcon,
  Trash2Icon,
  UploadIcon,
} from 'lucide-react';
import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
];

export function DocumentsStep() {
  const form = useFormContext<AddStudentSchema>();
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'documents',
  });

  const hasBackgroundInfo = fields.some(
    (doc) => doc.category === DocumentCategoryEnum.BACKGROUND
  );

  const [isDragging, setIsDragging] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFiles = (files: FileList | null) => {
    if (!files) {
      return;
    }

    for (const file of files) {
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        console.error(`File type ${file.type} not supported`);
        return;
      }

      if (file.size > MAX_FILE_SIZE) {
        console.error(`File ${file.name} is too large. Maximum size is 5MB.`);
        return;
      }

      append({
        file,
        category: DocumentCategoryEnum.BACKGROUND,
      });
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFiles(e.dataTransfer.files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
  };

  return (
    <div className="mb-6 space-y-6">
      <div>
        <h2 className="font-semibold text-lg">Documents</h2>
        <p className="text-muted-foreground text-sm">
          Upload relevant documents for the student. Documents are optional, but background information is required to activate the case.
        </p>
      </div>


      <If condition={!hasBackgroundInfo}>
        <div className="flex items-start gap-3 rounded-lg border border-dashed bg-muted/50 p-4 text-muted-foreground">
          <InfoIcon className="mt-0.5 size-5 flex-shrink-0" />
          <div>
            <p className="font-medium text-sm">Background Information Required</p>
            <p className="mt-1 text-xs">
              To activate the case, please upload a document in this step and assign it the "Background" category. Background information is required and must be provided if available.
            </p>
          </div>
        </div>
      </If>

      <div
        className={`relative cursor-pointer rounded-lg border border-dashed p-8 text-center text-sm transition-colors hover:bg-muted-foreground/5 ${isDragging
          ? 'border-primary bg-primary/5'
          : 'border-muted-foreground/25 hover:border-muted-foreground/50'
          }`}
        onClick={() => fileInputRef.current?.click()}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onKeyDown={() => null}
        role="button"
        tabIndex={0}
      >
        <input
          accept={ALLOWED_FILE_TYPES.join(',')}
          className="hidden"
          multiple
          onChange={handleFileInputChange}
          ref={fileInputRef}
          type="file"
        />
        <div className="mx-auto flex w-fit items-center justify-center rounded-lg bg-muted p-3">
          <UploadIcon className="size-4 text-muted-foreground" />
        </div>
        <h3 className="mt-4 font-medium">Upload documents</h3>
        <p className="mt-2 text-muted-foreground text-xs">
          Drag and drop or click to browse files
        </p>
        <p className="mt-4 text-muted-foreground text-xs">
          Accepts PDF, Word, Excel, and Text files (Max 5MB)
        </p>
      </div>

      <div className="grid gap-2">
        {fields.map((field, index) => (
          <DocumentCard
            document={field}
            index={index}
            key={field.id}
            onRemove={remove}
          />
        ))}
      </div>

      <If condition={!fields.length}>
        <p className="text-center text-muted-foreground text-sm">
          No documents uploaded yet.
        </p>
      </If>
    </div>
  );
}

interface DocumentCardProps {
  document: DocumentSchema;
  index: number;
  onRemove: (index: number) => void;
}

const DocumentCard = ({ document, index, onRemove }: DocumentCardProps) => {
  const form = useFormContext<AddStudentSchema>();
  const file = document.file;
  const fileName = file instanceof File ? file.name : file.name;
  const fileSize = file instanceof File ? file.size : file.size;
  const fileType = file instanceof File ? file.type : file.type;

  const currentCategory = form.watch(`documents.${index}.category`);

  const getFileIcon = (type: string) => {
    if (type === 'application/pdf') {
      return FilePdfIcon;
    }
    if (type.includes('spreadsheet') || type.includes('excel')) {
      return FileSpreadsheetIcon;
    }
    if (type.includes('word') || type === 'text/plain') {
      return FileTextIcon;
    }
    return FileIcon;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) {
      return '0 Bytes';
    }
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  };

  const handleCategoryChange = (newCategory: DocumentCategoryEnum) => {
    form.setValue(`documents.${index}.category`, newCategory);
  };

  const Icon = getFileIcon(fileType);

  return (
    <Card className="rounded-md py-2 shadow-none">
      <CardContent className="flex items-center gap-4 pr-1 pl-3">
        <Icon className="size-4 flex-shrink-0 text-muted-foreground" />

        <div className="flex min-w-0 flex-1 flex-col gap-1">
          <p className="truncate font-medium text-sm">{fileName}</p>
          <p className="text-muted-foreground text-xs">
            {formatFileSize(fileSize)} • {new Date().toLocaleDateString()}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Select onValueChange={handleCategoryChange} value={currentCategory}>
            <SelectTrigger className="w-40" size="sm">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(DocumentCategoryEnumMap).map(([key, label]) => (
                <SelectItem key={key} value={key}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            className="group size-8 flex-shrink-0 p-0"
            onClick={() => onRemove(index)}
            size="sm"
            type="button"
            variant="cancel"
          >
            <Trash2Icon className="size-4 text-muted-foreground group-hover:text-destructive" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};