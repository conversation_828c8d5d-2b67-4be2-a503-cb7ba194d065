'use client';

import type { ParentSchema } from '@lilypad/api/schemas/students';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import React from 'react';
import { ParentForm } from './parent-form';
import { ParentsList } from './parents-list';

interface ParentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  parents: ParentSchema[];
  onSave: (parents: ParentSchema[]) => void;
}

export function ParentDialog({
  open,
  onOpenChange,
  parents,
  onSave,
}: ParentDialogProps) {
  const [localParents, setLocalParents] = React.useState<ParentSchema[]>([]);
  const [editingIndex, setEditingIndex] = React.useState<number | null>(null);

  // Sync local parents with props when dialog opens
  React.useEffect(() => {
    if (open) {
      setLocalParents(parents);
    }
  }, [parents, open]);

  const handleAddParent = React.useCallback(
    (data: ParentSchema) => {
      setLocalParents((prev) => {
        if (editingIndex !== null) {
          // Update existing parent
          const updated = [...prev];
          updated[editingIndex] = data;
          setEditingIndex(null);
          return updated;
        }

        return [...prev, data];
      });
    },
    [editingIndex]
  );

  const handleEditParent = React.useCallback((index: number) => {
    setEditingIndex(index);
  }, []);

  const handleDeleteParent = React.useCallback(
    (index: number) => {
      setLocalParents((prev) => prev.filter((_, i) => i !== index));
      if (editingIndex === index) {
        setEditingIndex(null);
      }
    },
    [editingIndex]
  );

  const handleSave = React.useCallback(() => {
    onSave(localParents);
    onOpenChange(false);
    setEditingIndex(null);
  }, [localParents, onSave, onOpenChange]);

  const handleCancel = React.useCallback(() => {
    onOpenChange(false);
    setEditingIndex(null);
    setLocalParents(parents); // Reset to original parents
  }, [onOpenChange, parents]);

  const editingParent =
    editingIndex !== null ? localParents[editingIndex] : null;

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent
        className="flex max-h-[90vh] max-w-3xl flex-col p-0 md:max-w-5xl"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="border-b p-6 pb-4">
          <DialogTitle>Parents/Guardians</DialogTitle>
          <DialogDescription>
            Add parents or guardians for this student. At least one
            parent/guardian is required.
          </DialogDescription>
        </DialogHeader>

        <div className="grid min-h-0 flex-1 grid-cols-1 gap-4 px-6 md:grid-cols-2">
          <ParentForm
            editingParent={editingParent}
            isEditing={editingIndex !== null}
            onSubmit={handleAddParent}
          />

          <ParentsList
            onDeleteParent={handleDeleteParent}
            onEditParent={handleEditParent}
            parents={localParents}
          />
        </div>

        <DialogFooter className="rounded-b-lg bg-muted px-6 py-4">
          <Button onClick={handleCancel} size="sm" variant="outline">
            Cancel
          </Button>
          <Button
            disabled={localParents.length === 0}
            onClick={handleSave}
            size="sm"
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
