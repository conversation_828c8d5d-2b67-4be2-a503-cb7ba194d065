import {
  CasePriorityEnum,
  CaseTypeEnum,
  DocumentCategoryEnum,
  GenderEnum,
  ParentRelationshipEnum,
  SchoolGradeEnum,
} from '@lilypad/db/enums';
import { normalizePhone, validatePhone } from '@lilypad/shared/phone';
import z from 'zod';

export const getStudentsInputSchema = z.object({
  page: z.number().min(1).optional(),
  perPage: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.unknown(),
        isMulti: z.boolean().optional(),
      })
    )
    .optional(),
  joinOperator: z.enum(['and', 'or']).optional(),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional(),
});

export const parentSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  middleName: z.string().optional(),
  lastName: z.string().min(1, 'Last name is required'),
  relationshipType: z.nativeEnum(ParentRelationshipEnum),
  primaryEmail: z.string().email().optional(),
  secondaryEmail: z.string().email().optional(),
  primaryPhone: z
    .string()
    .refine((phoneNumber) => validatePhone(phoneNumber), {
      message: 'Invalid phone number format',
    })
    .transform((phoneNumber) => normalizePhone(phoneNumber))
    .optional(),
  secondaryPhone: z
    .string()
    .refine((phoneNumber) => validatePhone(phoneNumber), {
      message: 'Invalid phone number format',
    })
    .transform((phoneNumber) => normalizePhone(phoneNumber))
    .optional(),
  isPrimaryContact: z.boolean(),
  hasPickupAuthorization: z.boolean(),
});

export const serializableFileSchema = z.object({
  name: z.string(),
  size: z.number(),
  type: z.string(),
  data: z.string(), // Base64 encoded file content
});

export const documentSchema = z.object({
  file: z.union([z.instanceof(File), serializableFileSchema]),
  category: z
    .nativeEnum(DocumentCategoryEnum)
    .default(DocumentCategoryEnum.BACKGROUND)
    .optional(),
});

export const caseCreationSchema = z.object({
  caseType: z.nativeEnum(CaseTypeEnum),
  priority: z.nativeEnum(CasePriorityEnum),
  dateOfConsent: z.date({
    required_error: 'Date of consent is required',
  }),
});

export const notesSchema = z.object({
  content: z.any().optional(), // TipTap editor content
});

export const addStudentBaseSchema = z.object({
  id: z.string(), // Temporary ID for tracking
  studentIdNumber: z.string().min(1, 'Student ID is required'),
  firstName: z.string().min(1, 'First name is required'),
  middleName: z.string().optional(),
  lastName: z.string().min(1, 'Last name is required'),
  preferredName: z.string().optional(),
  dateOfBirth: z.date({
    required_error: 'Date of birth is required',
  }),
  grade: z.nativeEnum(SchoolGradeEnum, {
    required_error: 'Grade is required',
  }),
  gender: z.nativeEnum(GenderEnum, {
    required_error: 'Gender is required',
  }),
  primarySchoolId: z.string().min(1, 'School is required'),
  privateSchool: z.string().optional(),
  outOfDistrict: z.boolean().default(false),
  languageIds: z.array(z.string()).min(1, 'At least one language is required'),
  primaryLanguageId: z.string(),
  parents: z
    .array(parentSchema)
    .min(1, 'At least one parent or guardian is required'),
  documents: z.array(documentSchema).optional(),
  caseInfo: caseCreationSchema,
  notes: notesSchema.optional(),
});

export const addStudentSchema = addStudentBaseSchema
  .refine(
    (data) => {
      if (
        data.outOfDistrict &&
        (!data.privateSchool || data.privateSchool.trim() === '')
      ) {
        return false;
      }
      return true;
    },
    {
      message: 'Private school name is required',
      path: ['privateSchool'],
    }
  )
  .refine(
    (data) => {
      if (data.languageIds.length > 0 && !data.primaryLanguageId) {
        return false;
      }
      if (
        data.primaryLanguageId &&
        !data.languageIds.includes(data.primaryLanguageId)
      ) {
        return false;
      }
      return true;
    },
    {
      message:
        'Primary language must be selected and must be one of the selected languages',
      path: ['primaryLanguageId'],
    }
  )
  .refine(
    (data) => {
      // Ensure at least one parent is marked as primary contact
      return data.parents.some((parent) => parent.isPrimaryContact);
    },
    {
      message: 'At least one parent/guardian must be marked as primary contact',
      path: ['parents'],
    }
  );

export const studentSchema = z
  .object({
    id: z.string(), // Temporary ID for tracking
    studentIdNumber: z.string().min(1, 'Student ID is required'),
    firstName: z.string().min(1, 'First name is required'),
    middleName: z.string().optional(),
    lastName: z.string().min(1, 'Last name is required'),
    preferredName: z.string().optional(),
    dateOfBirth: z.date({
      required_error: 'Date of birth is required',
    }),
    dateOfConsent: z.date({
      required_error: 'Date of consent is required',
    }),
    grade: z.nativeEnum(SchoolGradeEnum, {
      required_error: 'Grade is required',
    }),
    gender: z.nativeEnum(GenderEnum, {
      required_error: 'Gender is required',
    }),
    primarySchoolId: z.string().min(1, 'School is required'),
    privateSchool: z.string().optional(),
    outOfDistrict: z.boolean().default(false),
    languageIds: z
      .array(z.string())
      .min(1, 'At least one language is required'),
    primaryLanguageId: z.string().nullable().default(null),
    parents: z
      .array(parentSchema)
      .min(1, 'At least one parent/guardian is required'),
    documents: z.array(documentSchema).default([]),
  })
  .refine(
    (data) => {
      // If outOfDistrict is true, privateSchool must be provided
      if (
        data.outOfDistrict &&
        (!data.privateSchool || data.privateSchool.trim() === '')
      ) {
        return false;
      }
      return true;
    },
    {
      message: 'Private school name is required',
      path: ['privateSchool'],
    }
  )
  .refine(
    (data) => {
      // If there are languages selected, primaryLanguageId must be set
      if (data.languageIds.length > 0 && !data.primaryLanguageId) {
        return false;
      }
      // If primaryLanguageId is set, it must be one of the selected languages
      if (
        data.primaryLanguageId &&
        !data.languageIds.includes(data.primaryLanguageId)
      ) {
        return false;
      }
      return true;
    },
    {
      message:
        'Primary language must be selected and must be one of the selected languages',
      path: ['primaryLanguageId'],
    }
  );

export const bulkStudentSchema = z.object({
  students: z.array(studentSchema).min(1, 'At least one student is required'),
});

// Keep the old name for backwards compatibility
export const bulkCreateStudentsInputSchema = bulkStudentSchema;

export const studentSaveResultSchema = z.object({
  tempId: z.string(),
  success: z.boolean(),
  studentId: z.string().optional(),
  error: z.string().optional(),
});

export type SerializableFileSchema = z.infer<typeof serializableFileSchema>;
export type ParentSchema = z.infer<typeof parentSchema>;
export type DocumentSchema = z.infer<typeof documentSchema>;
export type StudentSchema = z.infer<typeof studentSchema>;
export type BulkStudentSchema = z.infer<typeof bulkStudentSchema>;
export type StudentSaveResultSchema = z.infer<typeof studentSaveResultSchema>;

export type SerializableDocument = {
  file: SerializableFileSchema;
  category: DocumentCategoryEnum;
};

export type BulkCreateStudentsInput = z.infer<
  typeof bulkCreateStudentsInputSchema
>;

// Single student creation result
export const addStudentResultSchema = z.object({
  success: z.boolean(),
  studentId: z.string().optional(),
  caseId: z.string().optional(),
  error: z.string().optional(),
});

export type CaseCreationSchema = z.infer<typeof caseCreationSchema>;
export type NotesSchema = z.infer<typeof notesSchema>;
export type AddStudentBaseSchema = z.infer<typeof addStudentBaseSchema>;
export type AddStudentSchema = z.infer<typeof addStudentSchema>;
export type AddStudentResultSchema = z.infer<typeof addStudentResultSchema>;
