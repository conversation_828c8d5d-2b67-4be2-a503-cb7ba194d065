{"name": "@lilypad/shared", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@t3-oss/env-nextjs": "^0.13.4", "libphonenumber-js": "^1.12.10", "pino": "^9.7.0", "react-hook-form": "^7.56.4", "zod": "^3.25.7"}, "exports": {".": "./src/index.ts", "./date": "./src/date.ts", "./file-upload": "./src/file-upload.ts", "./http": "./src/http.ts", "./keys": "./src/keys.ts", "./logger": "./src/logger.ts", "./phone": "./src/phone.ts", "./errors": "./src/errors.ts", "./registry": "./src/registry.ts", "./routes": "./src/routes.ts", "./hooks/use-zod-form": "./src/hooks/use-zod-form.tsx", "./constants/timezones": "./src/constants/timezones.ts", "./lib/file-utils": "./src/lib/file-utils.ts"}}